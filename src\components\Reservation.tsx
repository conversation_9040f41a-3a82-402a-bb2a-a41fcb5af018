import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, User, Phone, Baby } from "lucide-react";

const Reservation = () => {
  const [checkIn, setCheckIn] = useState("");
  const [checkOut, setCheckOut] = useState("");
  const [adults, setAdults] = useState("2");
  const [children, setChildren] = useState("0");
  const [childrenAges, setChildrenAges] = useState<string[]>([]);
  
  // Villa Kent Termal Otel ID (rezervasyon sisteminden alınacak)
  const HOTEL_ID = "10649"; // Otel ID'nizi buraya girin

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!checkIn || !checkOut) {
      alert('Lütfen giriş ve çıkış tarihlerini seçiniz.');
      return;
    }
    
    // Çocuk yaşları kontrolü
    if (parseInt(children) > 0) {
      const missingAges = childrenAges.some(age => !age);
      if (missingAges) {
        alert('Lütfen tüm çocukların yaşlarını giriniz.');
        return;
      }
    }
    
    // Tarihleri URL formatına çevir (dd.mm.yyyy)
    const formatDate = (dateStr: string) => {
      const date = new Date(dateStr);
      return `${date.getDate()}.${date.getMonth() + 1}.${date.getFullYear()}`;
    };
    
    const formattedCheckIn = formatDate(checkIn);
    const formattedCheckOut = formatDate(checkOut);
    
    // URL parametrelerini oluştur
    const params = new URLSearchParams({
      eid: "1",
      hid: HOTEL_ID,
      ac: adults,
      cid: formattedCheckIn,
      cod: formattedCheckOut,
      cc: children
    });
    
    // Çocuk yaşlarını ekle
    childrenAges.forEach((age, index) => {
      if (age && index < 3) { // Maksimum 3 çocuk yaşı
        params.append(`c${index + 1}ad`, age);
      }
    });
    
    // Eksik çocuk yaş parametrelerini 0 ile doldur
    for (let i = childrenAges.length; i < 3; i++) {
      params.append(`c${i + 1}ad`, "0");
    }
    
    const reservationUrl = `https://gtr.otelresepsiyon.com/onlinerezervasyon/or/default2.aspx?${params.toString()}`;
    
    // Debug için URL'i konsola yazdır
    console.log('Rezervasyon URL:', reservationUrl);
    
    // Yeni sekmede rezervasyon sayfasını aç
    window.open(reservationUrl, '_blank');
  };
  
  // Çocuk sayısı değiştiğinde yaş array'ini güncelle
  const handleChildrenChange = (count: string) => {
    setChildren(count);
    const childCount = parseInt(count);
    const newAges = Array(childCount).fill("");
    setChildrenAges(newAges);
  };
  
  // Çocuk yaşını güncelle
  const updateChildAge = (index: number, age: string) => {
    const newAges = [...childrenAges];
    newAges[index] = age;
    setChildrenAges(newAges);
  };

  return (
    <section id="reservation" className="py-8 bg-gradient-to-br from-thermal-light/50 via-white to-thermal-light/30">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="text-center space-y-2 mb-6 animate-fade-in-up">
          <h2 className="text-2xl md:text-3xl font-bold text-thermal-dark">
            <span className="block text-thermal-blue">GÜVENLİ REZERVASYON
            OTELDE ÖDEME</span>
          </h2>
          <p className="text-sm text-muted-foreground max-w-lg mx-auto">
            Termal tatil deneyiminizi bugün planlayın.
          </p>
        </div>

        <div className="max-w-7xl mx-auto">
          <Card className="p-4 md:p-6 shadow-thermal bg-white/95 backdrop-blur-sm animate-fade-in-up">
            <div className="space-y-6">
              {/* Tarih ve Misafir Seçimi - Tek Satırda */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <label className="block text-xs font-semibold text-thermal-dark">
                    <Calendar className="w-3 h-3 inline mr-1 text-thermal-blue" />
                    Giriş
                  </label>
                  <input
                    type="date"
                    value={checkIn}
                    onChange={(e) => setCheckIn(e.target.value)}
                    className="w-full p-3 border-2 border-thermal-light rounded-lg focus:ring-2 focus:ring-thermal-blue focus:border-transparent transition-all duration-300 text-sm"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-xs font-semibold text-thermal-dark">
                    <Calendar className="w-3 h-3 inline mr-1 text-thermal-blue" />
                    Çıkış
                  </label>
                  <input
                    type="date"
                    value={checkOut}
                    onChange={(e) => setCheckOut(e.target.value)}
                    className="w-full p-3 border-2 border-thermal-light rounded-lg focus:ring-2 focus:ring-thermal-blue focus:border-transparent transition-all duration-300 text-sm"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-xs font-semibold text-thermal-dark">
                    <User className="w-3 h-3 inline mr-1 text-thermal-blue" />
                    Yetişkin
                  </label>
                  <select
                    value={adults}
                    onChange={(e) => setAdults(e.target.value)}
                    className="w-full p-3 border-2 border-thermal-light rounded-lg focus:ring-2 focus:ring-thermal-blue focus:border-transparent transition-all duration-300 text-sm"
                  >
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                    <option value="6">6</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <label className="block text-xs font-semibold text-thermal-dark">
                    <Baby className="w-3 h-3 inline mr-1 text-thermal-blue" />
                    Çocuk
                  </label>
                  <select
                    value={children}
                    onChange={(e) => handleChildrenChange(e.target.value)}
                    className="w-full p-3 border-2 border-thermal-light rounded-lg focus:ring-2 focus:ring-thermal-blue focus:border-transparent transition-all duration-300 text-sm"
                  >
                    <option value="0">0</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                  </select>
                </div>
              </div>
              
              {/* Çocuk Yaşları */}
              {parseInt(children) > 0 && (
                <div className="space-y-4 p-4 bg-thermal-light/20 rounded-lg">
                  <h3 className="text-sm font-semibold text-thermal-dark flex items-center">
                    <Baby className="w-4 h-4 mr-2 text-thermal-blue" />
                    Çocuk Yaşları
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {childrenAges.map((age, index) => (
                      <div key={index} className="space-y-1">
                        <label className="block text-xs font-medium text-thermal-dark">
                          {index + 1}. Çocuğun Yaşı
                        </label>
                        <select
                          value={age}
                          onChange={(e) => updateChildAge(index, e.target.value)}
                          className="w-full p-2 border-2 border-thermal-light rounded-lg focus:ring-2 focus:ring-thermal-blue focus:border-transparent text-sm"
                          required
                        >
                          <option value="">Yaş Seçiniz</option>
                          {Array.from({ length: 18 }, (_, i) => (
                            <option key={i} value={i.toString()}>{i} yaş</option>
                          ))}
                        </select>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Rezervasyon Butonu */}
              <div className="flex flex-col sm:flex-row gap-3 items-center justify-center">
                <Button
                  onClick={handleSubmit}
                  variant="thermal"
                  size="lg"
                  className="px-8 py-3 font-semibold shadow-thermal hover:shadow-lg transition-all duration-300 w-full sm:w-auto"
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  Rezervasyon Yap
                </Button>
                <span className="text-xs text-muted-foreground hidden sm:block">veya</span>
                <Button
                  variant="gold"
                  size="lg"
                  className="gap-2 px-6 py-3 w-full sm:w-auto"
                  onClick={() => window.open('tel:+902724124860')}
                >
                  <Phone className="w-4 h-4" />
                  +90 272 412 48 60
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default Reservation;