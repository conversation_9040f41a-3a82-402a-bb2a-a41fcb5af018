import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Calendar, User, Phone } from "lucide-react";
import { useTextSlideEffect } from "@/hooks/useTextSlideEffect";

const Hero = () => {
  const { visibleElements, elementRefs } = useTextSlideEffect();
  return (
    <section id="hero" className="min-h-screen relative overflow-hidden">
      {/* Background Image with Overlay */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('/images/hero/2.webp')`
        }}
      >
        <div className="absolute inset-0 bg-hero-gradient"></div>
      </div>

      {/* Content */}
      <div className="relative container mx-auto px-4 pt-24 pb-16">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          {/* Left Content */}
          <div 
            ref={(el) => (elementRefs.current[0] = el)}
            data-slide-index={0}
            className={`text-center lg:text-left text-white space-y-8 transition-all duration-1000 ease-out ${
              visibleElements.has(0) ? 'translate-x-0 opacity-100' : '-translate-x-full opacity-0'
            }`}
          >
            <div className="space-y-4">
              <div className="inline-block px-4 py-2 bg-thermal-gold/20 backdrop-blur-sm rounded-full border border-thermal-gold/30">
                <span className="text-thermal-gold font-semibold">🌊 Termal Tatil Fırsatları</span>
              </div>
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight">
                Muhteşem
                <span className="block text-thermal-gold">Termal Tatil</span>
              </h1>
              <p className="text-xl md:text-2xl text-white/90 max-w-2xl">
                Afyon'ın eşsiz termal sularında sağlık, huzur ve konforun buluştuğu unutulmaz tatil deneyimi.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button variant="gold" size="xl" className="gap-2">
                <Calendar className="w-5 h-5" />
                Şimdi Rezervasyon Yap
              </Button>
              <Button variant="thermalOutline" size="xl" className="gap-2 backdrop-blur-sm">
                <Phone className="w-5 h-5" />
                Hemen Ara
              </Button>
            </div>

            <div className="text-sm text-white/80">
              ⭐ Otelde Ödeme Seçeneği • Güvenli Rezervasyon • 7/24 Destek
            </div>
          </div>

          {/* Right Content - Booking Form */}
          <div 
            ref={(el) => (elementRefs.current[1] = el)}
            data-slide-index={1}
            className={`flex justify-center lg:justify-end transition-all duration-1000 ease-out delay-300 ${
              visibleElements.has(1) ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
            }`}
          >
            <Card className="w-full max-w-md p-6 bg-white/95 backdrop-blur-sm shadow-thermal">
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-thermal-dark mb-2">
                    Rezervasyon Yap
                  </h3>
                  <p className="text-muted-foreground">Otelde ödeme imkanı</p>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="text-sm font-medium text-thermal-dark block mb-2">
                        Giriş Tarihi
                      </label>
                      <div className="relative">
                        <input
                          type="date"
                          className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-thermal-dark block mb-2">
                        Çıkış Tarihi
                      </label>
                      <div className="relative">
                        <input
                          type="date"
                          className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="text-sm font-medium text-thermal-dark block mb-2">
                        Yetişkin
                      </label>
                      <select className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                      </select>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-thermal-dark block mb-2">
                        Çocuk
                      </label>
                      <select className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                  </div>

                  <Button variant="thermal" className="w-full" size="lg">
                    <User className="w-5 h-5 mr-2" />
                    Müsaitlik Sorgula
                  </Button>
                </div>

                <div className="text-center space-y-2">
                  <div className="text-xs text-muted-foreground">
                    Rezervasyon için hemen arayın
                  </div>
                  <div className="text-lg font-bold text-thermal-blue">
                    +90 272 444 0 444
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;