import { Button } from "@/components/ui/button";
import { Calendar, Phone } from "lucide-react";

const Hero = () => {
  return (
    <section id="hero" className="h-[530px] lg:h-[850px] relative overflow-hidden" role="banner" aria-label="Özbudak Termal Otel Ana Sayfa">
      {/* Desktop Background Image */}
      <div 
        className="absolute inset-0 bg-contain bg-center bg-no-repeat hidden lg:block"
        style={{
          backgroundImage: `url('/images/hero/desktop-hero.webp')`
        }}
        role="img"
        aria-label="Özbudak Termal Otel Gazlıgöl Afyonkarahisar"
      />
      
      {/* Mobile Background Image */}
      <div 
        className="absolute inset-0 bg-contain bg-center bg-no-repeat lg:hidden"
        style={{
          backgroundImage: `url('/images/hero/mobile-hero.webp')`
        }}
        role="img"
        aria-label="Özbudak Termal Otel Mobil Görünüm"
      />


    </section>
  );
};

export default Hero;