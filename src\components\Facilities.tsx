import { Card } from "@/components/ui/card";
import { Waves, Utensils, Dumbbell, Sparkles, Trees, GamepadIcon } from "lucide-react";
import { useImageSlideEffect } from "@/hooks/useImageSlideEffect";
import { useTextSlideEffect } from "@/hooks/useTextSlideEffect";

const Facilities = () => {
  const { visibleImages, imageRefs } = useImageSlideEffect();
  const { visibleElements, elementRefs } = useTextSlideEffect();
  const facilities = [
    {
      icon: Waves,
      title: "Termal Havuzlar",
      description: "Doğal termal sularla dolu havuzlarımızda sağlık ve huzur bulun. Farklı sıcaklıklarda termal havuz seçenekleri.",
      image: "https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      icon: Sparkles,
      title: "Spa & Wellness",
      description: "Profesyonel terapistlerimizle masaj, cilt bakımı ve sağlık hizmetleri. Kişiye özel wellness programları.",
      image: "https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      icon: Utensils,
      title: "Restoran & Bar",
      description: "Açık büfe kahvaltı, öğle ve akşam yemekleri. Yerel lezzetler ve uluslararası mutfak seçenekleri.",
      image: "https://images.unsplash.com/photo-1559339352-11d035aa65de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      icon: Dumbbell,
      title: "Fitness Center",
      description: "Modern ekipmanlarla donatılmış fitness salonumuzda spor yapmanın keyfini çıkarın.",
      image: "https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      icon: Trees,
      title: "Doğa Yürüyüşü",
      description: "60.000 m²'lik yeşil alanımızda doğa ile iç içe yürüyüş parkurları ve dinlendirici alanlar.",
      image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      icon: GamepadIcon,
      title: "Eğlence Merkezi",
      description: "Çocuklar için oyun alanları, masa tenisi, bilardo ve çeşitli aktivite seçenekleri.",
      image: "https://images.unsplash.com/photo-1511512578047-dfb367046420?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    }
  ];

  return (
    <section id="facilities" className="py-20 bg-thermal-light/20">
      <div className="container mx-auto px-4">
        <div 
          ref={(el) => (elementRefs.current[0] = el)}
          data-slide-index={0}
          className={`text-center space-y-4 mb-16 transition-all duration-1000 ease-out ${
            visibleElements.has(0) ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
          }`}
        >
          <div className="inline-block px-4 py-2 bg-thermal-teal/10 text-thermal-teal rounded-full font-semibold">
            Tesisler
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-thermal-dark">
            Otel
            <span className="block text-thermal-teal">Olanakları</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Konforlu konaklamanızın yanı sıra unutulmaz deneyimler sunan çeşitli tesis imkanlarımız.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {facilities.map((facility, index) => (
            <Card key={index} className="group overflow-hidden shadow-card-thermal hover:shadow-thermal transition-all duration-300">
              <div 
                ref={(el) => (imageRefs.current[index] = el)}
                data-index={index}
                className="relative overflow-hidden"
              >
                <img
                  src={facility.image}
                  alt={facility.title}
                  className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-${index % 4 === 0 ? 'r' : index % 4 === 1 ? 'b' : index % 4 === 2 ? 'l' : 't'} from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(index) ? 
                  (index % 4 === 0 ? 'translate-x-full' : 
                   index % 4 === 1 ? 'translate-y-full' : 
                   index % 4 === 2 ? '-translate-x-full' : '-translate-y-full') : 
                  (index % 4 === 1 || index % 4 === 3 ? 'translate-y-0' : 'translate-x-0')
                }`}></div>
                <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm p-3 rounded-full">
                  <facility.icon className="w-6 h-6 text-thermal-blue" />
                </div>
              </div>

              <div className="p-6 space-y-4">
                <h3 className="text-xl font-bold text-thermal-dark group-hover:text-thermal-blue transition-colors">
                  {facility.title}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {facility.description}
                </p>
              </div>
            </Card>
          ))}
        </div>

        <div className="mt-16 bg-thermal-gradient rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            Özel Sağlık Programları
          </h3>
          <p className="text-white/90 text-lg max-w-3xl mx-auto mb-6">
            Termal suların iyileştirici gücünden faydalanarak, uzman doktorlarımız eşliginde 
            kişiye özel sağlık ve rehabilitasyon programları sunuyoruz.
          </p>
          <div className="grid md:grid-cols-3 gap-6 text-center">
            <div className="space-y-2">
              <div className="text-3xl font-bold text-thermal-gold">7+</div>
              <div className="text-white/80">Termal Havuz</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-thermal-gold">24/7</div>
              <div className="text-white/80">Sağlık Hizmeti</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-thermal-gold">15+</div>
              <div className="text-white/80">Aktivite Seçeneği</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Facilities;