import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Waves, Utensils, Dumbbell, Sparkles, ChevronLeft, ChevronRight } from "lucide-react";
import { useImageSlideEffect } from "@/hooks/useImageSlideEffect";
import { useState } from "react";

const Facilities = () => {
  const { visibleImages, imageRefs } = useImageSlideEffect();
  const [activeSlides, setActiveSlides] = useState<Record<number, number>>({});
  
  const facilities = [
    {
      icon: Utensils,
      title: "Restoran",
      description: "Özbudak Termal’de, yarım pansiyon konsepti ile zengin kahvaltı menüsü ve çeşitli akşam yemeği lezzetleri ile muhafazakar ailelere özel bir deneyim sağlar. Her gün farklı temalarla sunulan akşam yemekleri, Gazlıgöl’ün huzurlu atmosferinde ailenizle birlikte keyifli anlar yaşatır. Ayrıca, rahatlatıcı kaplıca manzarasının eşliğinde, çeşitli atıştırmalıklar ve tatlılarla dolu öğleden sonra çay saati keyfinizi zenginleştirir",
      images: [
        "/images/gallery/OZBUDAK-RESTURANT1.webp",
        "/images/gallery/YA_5827-Kopya-scaled.webp",
        "/images/gallery/DSCF9935-scaled.webp"
      ]
    },
        {
      icon: Waves,
      title: "Termal Havuzlar",
      description: "Özbudak Termal, bay ve bayanlara özel yarı olimpik havuzları ile misafirlerine eşsiz bir yüzme deneyimi sunar. Hem spor hem de dinlenme için ideal olan bu havuzlar, enerjinizi yenileyip rahatlamanızı sağlar. Çocuklar için güvenli ve eğlenceli bir çocuk havuzu da mevcuttur, burada minik misafirler güven içinde eğlenirken, aileler de rahat bir nefes alabilir",
      images: [
        "/images/gallery/YA_5759-scaled (1).webp",
        "/images/gallery/ozbudak-buhar-odasi.webp"
      ]
    },
    {
      icon: Sparkles,
      title: "Spa & Wellness",
      description: "Özbudak Termal’de kendinizi yeniden keşfedin! Spa ve wellness alanımız, cildinizi buhar odasıyla tazeleyip, saunada kaslarınızı rahatlatarak beden ve ruh dengenizi geri kazandırır. Tuz odasındaki Himalaya tuzunun iyileştirici etkisi ile solunumunuzu güçlendirir, cildinizi canlandırır. Gazlıgöl’ün huzur dolu atmosferinde, arınma ve yenilenme deneyimini yaşayın..",
      images: [
        "/images/gallery/gorsel2.webp",
        "/images/gallery/ozbudak-sauna.webp",
        "/images/gallery/ozbudak-buhar-odasi.webp",
        "/images/gallery/ozbudak-tuz-odasi.webp"
      ]
    }

  ];

  const nextSlide = (facilityIndex: number) => {
    setActiveSlides(prev => ({
      ...prev,
      [facilityIndex]: ((prev[facilityIndex] || 0) + 1) % facilities[facilityIndex].images.length
    }));
  };

  const prevSlide = (facilityIndex: number) => {
    setActiveSlides(prev => ({
      ...prev,
      [facilityIndex]: ((prev[facilityIndex] || 0) - 1 + facilities[facilityIndex].images.length) % facilities[facilityIndex].images.length
    }));
  };

  return (
    <section id="facilities" className="py-20 bg-thermal-light/20">
      <div className="container mx-auto px-4">
        <div className="text-center space-y-4 mb-16 animate-fade-in-up">
          <div className="inline-block px-4 py-2 bg-thermal-teal/10 text-thermal-teal rounded-full font-semibold">
            Tesisler
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-thermal-dark">
            Otel
            <span className="block text-thermal-teal">Olanakları</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Konforlu konaklamanızın yanı sıra unutulmaz deneyimler sunan çeşitli tesis imkanlarımız.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 animate-fade-in-up">
          {facilities.map((facility, index) => {
            const currentSlide = activeSlides[index] || 0;
            return (
              <Card key={index} className="group overflow-hidden shadow-card-thermal hover:shadow-thermal transition-all duration-300">
                <div 
                  ref={(el) => (imageRefs.current[index] = el)}
                  data-index={index}
                  className="relative overflow-hidden"
                >
                  {/* Slider Container */}
                  <div className="relative h-48 overflow-hidden">
                    <div 
                      className="flex transition-transform duration-500 ease-in-out h-full"
                      style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                    >
                      {facility.images.map((image, imageIndex) => (
                        <img
                          key={imageIndex}
                          src={image}
                          alt={`${facility.title} ${imageIndex + 1}`}
                          className="w-full h-full object-cover flex-shrink-0 group-hover:scale-110 transition-transform duration-500"
                        />
                      ))}
                    </div>
                  </div>

                  {/* Slider Controls */}
                  <div className="absolute inset-0 flex items-center justify-between p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        prevSlide(index);
                      }}
                      className="bg-black/40 hover:bg-black/60 text-white rounded-full h-8 w-8"
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        nextSlide(index);
                      }}
                      className="bg-black/40 hover:bg-black/60 text-white rounded-full h-8 w-8"
                    >
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* Slide Indicators */}
                  <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                    {facility.images.map((_, imageIndex) => (
                      <button
                        key={imageIndex}
                        onClick={(e) => {
                          e.stopPropagation();
                          setActiveSlides(prev => ({
                            ...prev,
                            [index]: imageIndex
                          }));
                        }}
                        className={`w-2 h-2 rounded-full transition-all duration-300 ${
                          currentSlide === imageIndex 
                            ? 'bg-white' 
                            : 'bg-white/50 hover:bg-white/75'
                        }`}
                      />
                    ))}
                  </div>

                  {/* Gradient Overlay */}
                  <div className={`absolute inset-0 bg-gradient-to-${index % 4 === 0 ? 'r' : index % 4 === 1 ? 'b' : index % 4 === 2 ? 'l' : 't'} from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(index) ? 
                    (index % 4 === 0 ? 'translate-x-full' : 
                     index % 4 === 1 ? 'translate-y-full' : 
                     index % 4 === 2 ? '-translate-x-full' : '-translate-y-full') : 
                    (index % 4 === 1 || index % 4 === 3 ? 'translate-y-0' : 'translate-x-0')
                  }`}></div>

                  {/* Icon */}
                  <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm p-3 rounded-full">
                    <facility.icon className="w-6 h-6 text-thermal-blue" />
                  </div>
                </div>

                <div className="p-6 space-y-4">
                  <h3 className="text-xl font-bold text-thermal-dark group-hover:text-thermal-blue transition-colors">
                    {facility.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {facility.description}
                  </p>
                </div>
              </Card>
            );
          })}
        </div>

      </div>
    </section>
  );
};

export default Facilities;