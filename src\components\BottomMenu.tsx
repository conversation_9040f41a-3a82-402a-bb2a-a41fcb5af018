import { Home, Info, Bed, Building2,Utensils, Images, Mail } from "lucide-react";


const BottomMenu = () => {
  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const menuItems = [
    { id: "hero", icon: Home, label: "Ana Sayfa" },
    { id: "rooms", icon: Bed, label: "Odalar" },
    { id: "facilities", icon: Utensils, label: "Restoran" },
    { id: "gallery", icon: Images, label: "Galeri" },
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-thermal-light/20 shadow-lg md:hidden">
      <div className="grid grid-cols-4 py-2">
        {menuItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <button
              key={item.id}
              onClick={() => scrollToSection(item.id)}
              className="flex flex-col items-center justify-center py-2 px-1 text-gray-600 hover:text-thermal-blue transition-colors duration-200"
            >
              <IconComponent className="w-5 h-5 mb-1" />
              <span className="text-xs font-medium">{item.label}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default BottomMenu;
