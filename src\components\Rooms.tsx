import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Bed, Users, Wifi, Car, Coffee, Bath } from "lucide-react";
import { useImageSlideEffect } from "@/hooks/useImageSlideEffect";

const Rooms = () => {
  const { visibleImages, imageRefs } = useImageSlideEffect();
  const rooms = [
    {
      id: 1,
      name: "Suit 1+1 Oda",
      price: "2.750",
      originalPrice: "3.200",
      image: "/images/rooms/11.webp",
      capacity: "2 Kişi",
      features: ["Balkon", "Mutfak", "Termal Havuz", "LCD TV"],
      description: "Konforlu ve ferah standart odalarımız, termal tatilinizniz için ideal seçenekler sunuyor."
    },
  ];

  return (
    <section id="rooms" className="py-16 sm:py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="text-center space-y-4 mb-16 animate-fade-in-up">
          <div className="inline-block px-4 py-2 bg-thermal-blue/10 text-thermal-blue rounded-full font-semibold">
            Konaklama
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-thermal-dark">
            Odalarımız
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Her bütçeye uygun, konforlu ve ferah odalarımızla unutulmaz bir termal tatil deneyimi yaşayın.
          </p>
        </div>

                 <div className="flex justify-center animate-fade-in-up">
          <div className="max-w-2xl w-full px-4 sm:px-0">
            {rooms.map((room, index) => (
              <Card key={room.id} className="overflow-hidden shadow-card-thermal hover:shadow-thermal transition-all duration-300 group">
                <div 
                  ref={(el) => (imageRefs.current[index] = el)}
                  data-index={index}
                  className="relative overflow-hidden"
                >
                  <img
                    src={room.image}
                    alt={room.name}
                    className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className={`absolute inset-0 bg-gradient-to-${index === 0 ? 'r' : index === 1 ? 'b' : 'l'} from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(index) ? (index === 0 ? 'translate-x-full' : index === 1 ? 'translate-y-full' : '-translate-x-full') : (index === 1 ? 'translate-y-0' : 'translate-x-0')}`}></div>
                  <div className="absolute top-4 right-4 bg-thermal-gold text-thermal-dark px-3 py-1 rounded-full font-bold text-sm">
                    %15 İndirim
                  </div>
                </div>

                <div className="p-6 space-y-4">
                  <div className="space-y-2">
                    <h3 className="text-xl font-bold text-thermal-dark">{room.name}</h3>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Users className="w-4 h-4" />
                      <span>{room.capacity}</span>
                    </div>
                  </div>

                  <p className="text-muted-foreground text-sm">
                    {room.description}
                  </p>

                  <div className="grid grid-cols-2 gap-2">
                    {room.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <div className="w-2 h-2 bg-thermal-teal rounded-full"></div>
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className="border-t pt-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm text-muted-foreground line-through">
                          {room.originalPrice} TL
                        </div>
                        <div className="text-2xl font-bold text-thermal-blue">
                          {room.price} TL
                        </div>
                        <div className="text-sm text-muted-foreground">
                          kişi başı / gece
                        </div>
                      </div>
                    </div>

                    <Button variant="thermal" className="w-full">
                      Rezervasyon Yap
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

                 <div className="text-center mt-12 animate-fade-in-up">
          <div className="inline-flex flex-col sm:flex-row items-center gap-4 bg-thermal-light/50 backdrop-blur-sm p-4 rounded-xl">
            <div className="flex items-center gap-2">
              <Wifi className="w-5 h-5 text-thermal-blue" />
              <span className="font-medium">Ücretsiz WiFi</span>
            </div>
            <div className="flex items-center gap-2">
              <Car className="w-5 h-5 text-thermal-blue" />
              <span className="font-medium">Ücretsiz Otopark</span>
            </div>
            <div className="flex items-center gap-2">
              <Coffee className="w-5 h-5 text-thermal-blue" />
              <span className="font-medium">Yarım Pansiyon</span>
            </div>
            <div className="flex items-center gap-2">
              <Bath className="w-5 h-5 text-thermal-blue" />
              <span className="font-medium">Termal Havuz</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Rooms;