import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Phone, Menu, X } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const isMobile = useIsMobile();

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
      setIsMenuOpen(false);
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-border shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <img
              src="/images/logo/bg-logo-1.webp"
              alt="Termal Hotel Logo"
              className="w-28  object-contain"
            />
          </div>

          {/* Desktop Navigation */}
          {!isMobile && (
            <nav className="hidden md:flex items-center space-x-8">
              <button
                onClick={() => scrollToSection("hero")}
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                Ana Sayfa
              </button>
              <button
                onClick={() => scrollToSection("about")}
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                Hakkımızda
              </button>
              <button
                onClick={() => scrollToSection("rooms")}
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                Odalar
              </button>
              <button
                onClick={() => scrollToSection("facilities")}
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                Tesisler
              </button>
              <button
                onClick={() => scrollToSection("gallery")}
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                Galeri
              </button>
              <button
                onClick={() => scrollToSection("contact")}
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                İletişim
              </button>
            </nav>
          )}

          {/* Desktop Actions */}
          {!isMobile && (
            <div className="hidden md:flex items-center space-x-4">
              <Button variant="ghost" size="sm" className="gap-2">
                <Phone className="w-4 h-4" />
                <span className="font-semibold">+90 272 444 0 444</span>
              </Button>
              <Button variant="thermal" size="lg">
                Rezervasyon Yap
              </Button>
            </div>
          )}

          {/* Mobile Actions */}
          {isMobile && (
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" className="gap-1 px-2">
                <Phone className="w-4 h-4" />
                <span className="text-xs font-semibold">Ara</span>
              </Button>
              <Button variant="thermal" size="sm">
                Rezervasyon
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </Button>
            </div>
          )}
        </div>

        {/* Mobile Menu */}
        {isMobile && isMenuOpen && (
          <div className="md:hidden border-t border-border bg-white/95 backdrop-blur-md">
            <nav className="py-4 space-y-2">
              <button
                onClick={() => scrollToSection("hero")}
                className="block w-full text-left px-4 py-2 text-foreground hover:bg-secondary transition-colors font-medium"
              >
                Ana Sayfa
              </button>
              <button
                onClick={() => scrollToSection("about")}
                className="block w-full text-left px-4 py-2 text-foreground hover:bg-secondary transition-colors font-medium"
              >
                Hakkımızda
              </button>
              <button
                onClick={() => scrollToSection("rooms")}
                className="block w-full text-left px-4 py-2 text-foreground hover:bg-secondary transition-colors font-medium"
              >
                Odalar
              </button>
              <button
                onClick={() => scrollToSection("facilities")}
                className="block w-full text-left px-4 py-2 text-foreground hover:bg-secondary transition-colors font-medium"
              >
                Tesisler
              </button>
              <button
                onClick={() => scrollToSection("gallery")}
                className="block w-full text-left px-4 py-2 text-foreground hover:bg-secondary transition-colors font-medium"
              >
                Galeri
              </button>
              <button
                onClick={() => scrollToSection("contact")}
                className="block w-full text-left px-4 py-2 text-foreground hover:bg-secondary transition-colors font-medium"
              >
                İletişim
              </button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;