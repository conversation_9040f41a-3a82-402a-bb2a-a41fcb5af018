import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Phone, Menu, X, Home, Info, Bed, Building2, Images, Mail } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const isMobile = useIsMobile();

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
      setIsMenuOpen(false);
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-xl border-b border-thermal-light/20 shadow-thermal">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="flex items-center justify-between h-16 sm:h-20">
          {/* Logo */}
          <div className="flex items-center space-x-3 animate-fade-in-left group">
            <div className="relative">
              <img
                src="/images/logo/bg-logo-1.webp"
                alt="Termal Hotel Logo"
                className="w-20 sm:w-24 md:w-28 object-contain transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-thermal-gold/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>

          {/* Desktop Navigation */}
          {!isMobile && (
            <nav className="hidden lg:flex items-center space-x-6 xl:space-x-8 animate-fade-in-up">
              {[
                { id: "hero", label: "Ana Sayfa", icon: Home },
                { id: "about", label: "Hakkımızda", icon: Info },
                { id: "rooms", label: "Odalar", icon: Bed },
                { id: "facilities", label: "Tesisler", icon: Building2 },
                { id: "gallery", label: "Galeri", icon: Images },
                { id: "contact", label: "İletişim", icon: Mail }
              ].map((item) => {
                const IconComponent = item.icon;
                return (
                  <Button
                    key={item.id}
                    variant="default"
                    size="sm"
                    onClick={() => scrollToSection(item.id)}
                    className="min-w-24 gap-2 px-3 py-2 relative group overflow-hidden"
                  >
                    <IconComponent className="w-4 h-4" />
                    {item.label}
                    <div className="absolute inset-0 bg-gradient-to-r from-thermal-blue to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                  </Button>
                );
              })}
            </nav>
          )}

          {/* Desktop Actions */}
          {!isMobile && (
            <div className="hidden lg:flex items-center space-x-4 animate-fade-in-right">
              <Button 
                variant="thermal" 
                size="sm" 
                className="gap-2 px-4 py-2 min-w-48 hover:shadow-lg transition-all duration-300"
                onClick={() => window.open('tel:+902724440444')}
              >
                <Phone className="w-4 h-4" />
                <span className="font-semibold">+90 272 412 48 66</span>
              </Button>
            </div>
          )}

          {/* Mobile/Tablet Actions */}
          {(isMobile || (typeof window !== 'undefined' && window.innerWidth < 1024)) && (
            <div className="flex items-center space-x-3">
              <Button 
                variant="thermal" 
                size="sm" 
                className="gap-2 px-3 py-2 min-w-16 hidden sm:flex hover:shadow-lg transition-all duration-300"
                onClick={() => window.open('tel:+902724440444')}
              >
                <Phone className="w-4 h-4" />
                <span className="text-xs font-semibold">Ara</span>
              </Button>

              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="sm:hidden w-10 h-10 min-w-10 hover:bg-thermal-light/20 hover:text-thermal-blue transition-all duration-300 rounded-xl"
              >
                {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </Button>
            </div>
          )}
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-thermal-light/20 bg-white/98 backdrop-blur-xl relative z-40">
            <nav className="py-6 px-6 space-y-4">
              {[
                { id: "hero", label: "Ana Sayfa", icon: Home },
                { id: "about", label: "Hakkımızda", icon: Info },
                { id: "rooms", label: "Odalar", icon: Bed },
                { id: "facilities", label: "Tesisler", icon: Building2 },
                { id: "gallery", label: "Galeri", icon: Images },
                { id: "contact", label: "İletişim", icon: Mail }
              ].map((item) => {
                const IconComponent = item.icon;
                return (
                  <Button
                    key={item.id}
                    variant="outline"
                    size="sm"
                    onClick={() => scrollToSection(item.id)}
                    className="w-full min-w-full justify-start gap-3 px-4 py-3 border-2 bg-transparent hover:bg-thermal-blue hover:text-white transition-all duration-300 font-medium rounded-lg group relative overflow-hidden"
                  >
                    <IconComponent className="w-4 h-4" />
                    {item.label}
                    <div className="absolute inset-0 bg-gradient-to-r from-thermal-blue to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                  </Button>
                );
              })}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;