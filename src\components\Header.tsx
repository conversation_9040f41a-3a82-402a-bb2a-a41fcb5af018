import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Phone, Home, Info, Bed, Building2, Images, Mail, Headphones, MessageCircle } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

const Header = () => {
  const [isSupportModalOpen, setIsSupportModalOpen] = useState(false);
  const isMobile = useIsMobile();

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleCall = () => {
    window.open("tel:+902724440444", "_self");
    setIsSupportModalOpen(false);
  };

  const handleCallBack = () => {
    // Callback form açılabilir veya telefon numarası alınabilir
    alert("Size geri dönüş yapacağız. Telefon numaranızı bırakın.");
    setIsSupportModalOpen(false);
  };

  const handleWhatsApp = () => {
    const message = "Merhaba! Termal otel hakkında bilgi almak istiyorum.";
    const url = `https://wa.me/905551234567?text=${encodeURIComponent(message)}`;
    window.open(url, "_blank");
    setIsSupportModalOpen(false);
  };

  const handleOtherSupport = () => {
    scrollToSection("contact");
    setIsSupportModalOpen(false);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-xl border-b border-thermal-light/20 shadow-thermal">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="flex items-center justify-between h-16 sm:h-20">
          {/* Logo */}
          <div className="flex items-center space-x-3 animate-fade-in-left group">
            <div className="relative">
              <img
                src="/images/logo/bg-logo-1.webp"
                alt="Termal Hotel Logo"
                className="w-20 sm:w-24 md:w-28 object-contain transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-thermal-gold/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>

          {/* Desktop Navigation */}
          {!isMobile && (
            <nav className="hidden lg:flex items-center space-x-6 xl:space-x-8 animate-fade-in-up">
              {[
                { id: "hero", label: "Ana Sayfa", icon: Home },
                { id: "about", label: "Hakkımızda", icon: Info },
                { id: "rooms", label: "Odalar", icon: Bed },
                { id: "facilities", label: "Tesisler", icon: Building2 },
                { id: "gallery", label: "Galeri", icon: Images },
                { id: "contact", label: "İletişim", icon: Mail }
              ].map((item) => {
                const IconComponent = item.icon;
                return (
                  <Button
                    key={item.id}
                    variant="default"
                    size="sm"
                    onClick={() => scrollToSection(item.id)}
                    className="min-w-24 gap-2 px-3 py-2 relative group overflow-hidden"
                  >
                    <IconComponent className="w-4 h-4" />
                    {item.label}
                    <div className="absolute inset-0 bg-gradient-to-r from-thermal-blue to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                  </Button>
                );
              })}
            </nav>
          )}

          {/* Desktop Actions */}
          {!isMobile && (
            <div className="hidden lg:flex items-center space-x-4 animate-fade-in-right">
              <Button 
                variant="thermal" 
                size="sm" 
                className="gap-2 px-4 py-2 min-w-48 hover:shadow-lg transition-all duration-300"
                onClick={() => window.open('tel:+902724440444')}
              >
                <Phone className="w-4 h-4" />
                <span className="font-semibold">+90 272 412 48 66</span>
              </Button>
            </div>
          )}

          {/* Mobile/Tablet Actions */}
          {(isMobile || (typeof window !== 'undefined' && window.innerWidth < 1024)) && (
            <div className="flex items-center space-x-3">
              <Button 
                variant="thermal" 
                size="sm" 
                className="gap-2 px-3 py-2 min-w-16 hidden sm:flex hover:shadow-lg transition-all duration-300"
                onClick={() => window.open('tel:+902724440444')}
              >
                <Phone className="w-4 h-4" />
                <span className="text-xs font-semibold">Ara</span>
              </Button>

              <Button
                variant="thermal"
                size="sm"
                onClick={() => setIsSupportModalOpen(true)}
                className="sm:hidden gap-2 px-3 py-2 rounded-full hover:shadow-lg transition-all duration-300"
              >
                <Headphones className="w-4 h-4" />
                <span className="text-sm font-semibold">Destek</span>
              </Button>
            </div>
          )}
        </div>


      </div>

      {/* Destek Modal */}
      <Dialog open={isSupportModalOpen} onOpenChange={setIsSupportModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl font-bold text-thermal-dark">
              Destek
            </DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4 py-4">
            <Button
              onClick={handleCall}
              variant="thermal"
              className="flex flex-col items-center gap-2 h-20 rounded-full hover:shadow-lg transition-all duration-300"
            >
              <Phone className="w-6 h-6" />
              <span className="text-sm font-semibold">Hemen Ara</span>
            </Button>

            <Button
              onClick={handleCallBack}
              variant="thermal"
              className="flex flex-col items-center gap-2 h-20 rounded-full hover:shadow-lg transition-all duration-300"
            >
              <Phone className="w-6 h-6" />
              <span className="text-sm font-semibold">Sizi Arayalım</span>
            </Button>

            <Button
              onClick={handleWhatsApp}
              variant="whatsapp"
              className="flex flex-col items-center gap-2 h-20 rounded-full hover:shadow-lg transition-all duration-300"
            >
              <MessageCircle className="w-6 h-6" />
              <span className="text-sm font-semibold">WhatsApp</span>
            </Button>

            <Button
              onClick={handleOtherSupport}
              variant="secondary"
              className="flex flex-col items-center gap-2 h-20 rounded-full hover:shadow-lg transition-all duration-300"
            >
              <Mail className="w-6 h-6" />
              <span className="text-sm font-semibold">Şimdi Sor</span>
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </header>
  );
};

export default Header;