import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Phone, Home, Info, Bed, Building2, Images, Mail, Headphones } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

const Header = () => {
  const [isSupportModalOpen, setIsSupportModalOpen] = useState(false);
  const isMobile = useIsMobile();

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleCall = () => {
    window.open("tel:+902724440444", "_self");
    setIsSupportModalOpen(false);
  };

  const handleCallBack = () => {
    // Callback form açılabilir veya telefon numarası alınabilir
    alert("Size geri dönüş yapacağız. Telefon numaranızı bırakın.");
    setIsSupportModalOpen(false);
  };

  const handleWhatsApp = () => {
    const message = "Merhaba! Termal otel hakkında bilgi almak istiyorum.";
    const url = `https://wa.me/905551234567?text=${encodeURIComponent(message)}`;
    window.open(url, "_blank");
    setIsSupportModalOpen(false);
  };

  const handleOtherSupport = () => {
    scrollToSection("contact");
    setIsSupportModalOpen(false);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-xl border-b border-thermal-light/20 shadow-thermal">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="flex items-center justify-between h-16 sm:h-20">
          {/* Logo */}
          <div className="flex items-center space-x-3 animate-fade-in-left group">
            <div className="relative">
              <img
                src="/images/logo/bg-logo-1.webp"
                alt="Termal Hotel Logo"
                className="w-20 sm:w-24 md:w-28 object-contain transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-thermal-gold/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>

          {/* Desktop Navigation */}
          {!isMobile && (
            <nav className="hidden lg:flex items-center space-x-6 xl:space-x-8 animate-fade-in-up">
              {[
                { id: "hero", label: "Ana Sayfa", icon: Home },
                { id: "about", label: "Hakkımızda", icon: Info },
                { id: "rooms", label: "Odalar", icon: Bed },
                { id: "facilities", label: "Tesisler", icon: Building2 },
                { id: "gallery", label: "Galeri", icon: Images },
                { id: "contact", label: "İletişim", icon: Mail }
              ].map((item) => {
                const IconComponent = item.icon;
                return (
                  <Button
                    key={item.id}
                    variant="default"
                    size="sm"
                    onClick={() => scrollToSection(item.id)}
                    className="min-w-24 gap-2 px-3 py-2 relative group overflow-hidden"
                  >
                    <IconComponent className="w-4 h-4" />
                    {item.label}
                    <div className="absolute inset-0 bg-gradient-to-r from-thermal-blue to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                  </Button>
                );
              })}
            </nav>
          )}

          {/* Desktop Actions */}
          {!isMobile && (
            <div className="hidden lg:flex items-center space-x-4 animate-fade-in-right">
              <Button 
                variant="thermal" 
                size="sm" 
                className="gap-2 px-4 py-2 min-w-48 hover:shadow-lg transition-all duration-300"
                onClick={() => window.open('tel:+902724440444')}
              >
                <Phone className="w-4 h-4" />
                <span className="font-semibold">+90 272 412 48 66</span>
              </Button>
            </div>
          )}

          {/* Mobile/Tablet Actions */}
          {(isMobile || (typeof window !== 'undefined' && window.innerWidth < 1024)) && (
            <div className="flex items-center space-x-3">
              <Button 
                variant="thermal" 
                size="sm" 
                className="gap-2 px-3 py-2 min-w-16 hidden sm:flex hover:shadow-lg transition-all duration-300"
                onClick={() => window.open('tel:+902724440444')}
              >
                <Phone className="w-4 h-4" />
                <span className="text-xs font-semibold">Ara</span>
              </Button>

              <Button
                variant="thermal"
                size="sm"
                onClick={() => setIsSupportModalOpen(true)}
                className="sm:hidden gap-2 px-3 py-2 rounded-full hover:shadow-lg transition-all duration-300"
              >
                <Headphones className="w-4 h-4" />
                <span className="text-sm font-semibold">Destek</span>
              </Button>
            </div>
          )}
        </div>


      </div>

      {/* Destek Modal */}
      {isSupportModalOpen && (
        <div className="fixed inset-0 z-50 flex items-start justify-center pt-20 px-4">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => setIsSupportModalOpen(false)}
          ></div>
          <div className="relative bg-white rounded-lg shadow-2xl w-full max-w-md overflow-hidden transition-all duration-300 ease-out transform opacity-100 translate-y-0 scale-100">
            <div className="p-5">
              <div className="flex justify-between items-center mb-4 border-b pb-2">
                <h3 className="text-lg font-semibold text-gray-800">Destek Seçenekleri</h3>
                <button
                  onClick={() => setIsSupportModalOpen(false)}
                  className="text-gray-500 hover:text-gray-700 focus:outline-none"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"></path>
                  </svg>
                </button>
              </div>
              <div className="grid grid-cols-2 gap-4 animate-fade-in">
                <a
                  href="tel:+902724440444"
                  className="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200 transform hover:scale-[1.02] text-center"
                >
                  <div className="bg-blue-100 p-2 rounded-full mb-2">
                    <Phone className="h-6 w-6 text-blue-600" />
                  </div>
                  <span className="block font-medium text-gray-800">Hemen Ara</span>
                  <span className="text-xs text-gray-500">Müşteri temsilcisi</span>
                </a>

                <button
                  onClick={handleCallBack}
                  className="flex flex-col items-center p-4 bg-rose-50 hover:bg-rose-100 rounded-lg transition-colors duration-200 transform hover:scale-[1.02] text-center"
                >
                  <div className="bg-rose-100 p-2 rounded-full mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-rose-600">
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                      <path d="M14.05 2a9 9 0 0 1 8 7.94"></path>
                      <path d="M14.05 6A5 5 0 0 1 18 10"></path>
                    </svg>
                  </div>
                  <span className="block font-medium text-gray-800">Sizi Arayalım</span>
                  <span className="text-xs text-gray-500">Biz sizi arayalım</span>
                </button>

                <a
                  href="https://wa.me/905551234567"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors duration-200 transform hover:scale-[1.02] text-center"
                >
                  <div className="bg-green-100 p-2 rounded-full mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"></path>
                    </svg>
                  </div>
                  <span className="block font-medium text-gray-800">WhatsApp</span>
                  <span className="text-xs text-gray-500">Hızlı mesaj</span>
                </a>

                <button
                  onClick={handleOtherSupport}
                  className="flex flex-col items-center p-4 bg-amber-50 hover:bg-amber-100 rounded-lg transition-colors duration-200 transform hover:scale-[1.02] text-center"
                >
                  <div className="bg-amber-100 p-2 rounded-full mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-amber-600">
                      <circle cx="12" cy="12" r="10"></circle>
                      <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                      <path d="M12 17h.01"></path>
                    </svg>
                  </div>
                  <span className="block font-medium text-gray-800">Şimdi Sor</span>
                  <span className="text-xs text-gray-500">Yardım merkezi</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;