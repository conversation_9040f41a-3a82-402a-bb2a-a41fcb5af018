import { But<PERSON> } from "@/components/ui/button";
import { MessageCircle, Phone, Calendar, ChevronUp } from "lucide-react";

const BottomActionButtons = () => {
  const scrollToReservation = () => {
    const element = document.getElementById('reservation');
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleWhatsApp = () => {
    const message = encodeURIComponent("Merhaba! Villa Kent Termal Otel hakkında bilgi almak istiyorum.");
    window.open(`https://wa.me/+902724124866?text=${message}`, '_blank');
  };

  const handleCall = () => {
    window.open('tel:+902724124866');
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="fixed bottom-16 left-0 right-0 z-50 lg:hidden">
      {/* Glassmorphism container */}
        
          <div className="flex justify-between items-center">
            
            {/* WhatsApp Button */}
            <Button
              onClick={handleWhatsApp}
              className="group relative flex flex-col items-center gap-1 bg-gradient-to-br from-green-400 to-green-600 hover:from-green-500 hover:to-green-700 text-white py-2 px-6 h-auto border-0 flex-1"
            >
              <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <MessageCircle className="w-6 h-6 relative z-10" />
              <span className="text-xs font-semibold relative z-10">WhatsApp</span>
            </Button>

            {/* Call Button */}
            <Button
              onClick={handleCall}
              className="group relative flex flex-col items-center gap-1 bg-gradient-to-br from-pink-400 to-rose-600 hover:from-pink-500 hover:to-rose-700 text-white py-2 px-6 h-auto border-0 flex-1"
            >
              <div className="absolute inset-0 bg-white/20  opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <Phone className="w-6 h-6 relative z-10" />
              <span className="text-xs font-semibold relative z-10">Hemen Ara</span>
            </Button>

            {/* Reservation Button */}
            <Button
              onClick={scrollToReservation}
              className="group relative flex flex-col items-center gap-1 bg-gradient-to-br from-orange-400 to-amber-600 hover:from-orange-500 hover:to-amber-700 text-white py-2 px-6 h-auto border-0 flex-1"
            >
              <div className="absolute inset-0 bg-white/20  opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <Calendar className="w-6 h-6 relative z-10" />
              <span className="text-xs font-semibold relative z-10">Rezervasyon</span>
            </Button>

            {/* Back to Top Button */}
            <Button
              onClick={scrollToTop}
              className="group relative flex flex-col items-center gap-1 bg-gradient-to-br from-blue-400 to-blue-600 hover:from-blue-500 hover:to-blue-700 text-white py-2 px-6 h-auto border-0 flex-1"
            >
              <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <ChevronUp className="w-6 h-6 relative z-10" />
              <span className="text-xs font-semibold relative z-10">Yukarı</span>
            </Button>

          </div>
        </div>
  );
};

export default BottomActionButtons;