import { MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

const WhatsAppButton = () => {
  const whatsappNumber = "+905551234567"; // Gerçek WhatsApp numarası buraya eklenecek
  const message = "Merhaba! Termal otel rezervasyonu hakkında bilgi almak istiyorum.";

  const handleWhatsAppClick = () => {
    const url = `https://wa.me/${whatsappNumber.replace(/[^\d]/g, '')}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Button
        onClick={handleWhatsAppClick}
        variant="whatsapp"
        size="lg"
        className="rounded-full shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse hover:animate-none gap-2 px-6"
      >
        <MessageCircle className="w-5 h-5" />
        <span className="hidden sm:inline">WhatsApp</span>
      </Button>
    </div>
  );
};

export default WhatsAppButton;