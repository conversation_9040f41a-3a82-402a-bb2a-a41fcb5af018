@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 200 20% 98%;
    --foreground: 200 15% 8%;

    --card: 0 0% 100%;
    --card-foreground: 200 15% 8%;

    --popover: 0 0% 100%;
    --popover-foreground: 200 15% 8%;

    --primary: 195 100% 35%;
    --primary-foreground: 0 0% 98%;

    --secondary: 190 60% 92%;
    --secondary-foreground: 195 100% 25%;

    --muted: 190 60% 95%;
    --muted-foreground: 200 15% 40%;

    --accent: 180 100% 85%;
    --accent-foreground: 195 100% 25%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 190 30% 88%;
    --input: 190 30% 88%;
    --ring: 195 100% 35%;

    /* Thermal Hotel Specific Colors */
    --thermal-blue: 195 100% 35%;
    --thermal-teal: 180 100% 40%;
    --thermal-light: 190 60% 95%;
    --thermal-gold: 45 100% 60%;
    --thermal-dark: 200 15% 15%;

    /* Gradients */
    --gradient-thermal: linear-gradient(135deg, hsl(var(--thermal-blue)), hsl(var(--thermal-teal)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--thermal-blue) / 0.9), hsl(var(--thermal-teal) / 0.8));
    --gradient-card: linear-gradient(145deg, hsl(var(--card)), hsl(var(--thermal-light) / 0.5));

    /* Shadows */
    --shadow-thermal: 0 10px 30px -10px hsl(var(--thermal-blue) / 0.3);
    --shadow-card: 0 4px 20px -4px hsl(var(--thermal-blue) / 0.15);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 200 15% 5%;
    --foreground: 190 60% 95%;

    --card: 200 15% 8%;
    --card-foreground: 190 60% 95%;

    --popover: 200 15% 8%;
    --popover-foreground: 190 60% 95%;

    --primary: 195 100% 45%;
    --primary-foreground: 200 15% 5%;

    --secondary: 195 20% 15%;
    --secondary-foreground: 190 60% 95%;

    --muted: 195 20% 12%;
    --muted-foreground: 190 30% 65%;

    --accent: 180 50% 20%;
    --accent-foreground: 190 60% 95%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 195 20% 18%;
    --input: 195 20% 18%;
    --ring: 195 100% 45%;

    /* Dark mode thermal colors */
    --thermal-blue: 195 100% 45%;
    --thermal-teal: 180 100% 50%;
    --thermal-light: 195 20% 12%;
    --thermal-gold: 45 100% 65%;
    --thermal-dark: 200 15% 5%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}