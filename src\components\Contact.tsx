import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Phone, Mail, MapPin, Clock, MessageCircle } from "lucide-react";

const Contact = () => {
  return (
    <section id="contact" className="py-20 bg-thermal-light/30">
      <div className="container mx-auto px-4">
        <div className="text-center space-y-4 mb-16 animate-fade-in-up">
          <div className="inline-block px-4 py-2 bg-thermal-teal/10 text-thermal-teal rounded-full font-semibold">
            İletişim
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-thermal-dark">
            Bizimle
            <span className="block text-thermal-teal">İletişime Geçin</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Rezervasyon yapmak veya sorularınız için 7/24 hizmetinizdeyiz.
          </p>
        </div>

                 <div className="grid lg:grid-cols-2 gap-12 animate-fade-in-up">
          {/* Contact Info */}
          <div className="space-y-8 animate-fade-in-left">
            <Card className="p-6 shadow-card-thermal">
              <div className="flex items-start space-x-4">
                <div className="bg-thermal-blue/10 p-3 rounded-lg">
                  <Phone className="w-6 h-6 text-thermal-blue" />
                </div>
                <div className="space-y-1">
                  <h3 className="font-semibold text-thermal-dark">Rezervasyon Hattı</h3>
                  <div className="text-2xl font-bold text-thermal-blue">+90 272 412 48 66</div>
                  <p className="text-muted-foreground text-sm">7/24 rezervasyon hizmeti</p>
                </div>
              </div>
            </Card>

            <Card className="p-6 shadow-card-thermal">
              <div className="flex items-start space-x-4">
                <div className="bg-thermal-teal/10 p-3 rounded-lg">
                  <Mail className="w-6 h-6 text-thermal-teal" />
                </div>
                <div className="space-y-1">
                  <h3 className="font-semibold text-thermal-dark">E-posta</h3>
                  <div className="text-lg font-medium text-thermal-teal"><EMAIL></div>
                  <p className="text-muted-foreground text-sm">Sorularınız için yazabilirsiniz</p>
                </div>
              </div>
            </Card>

            <Card className="p-6 shadow-card-thermal">
              <div className="flex items-start space-x-4">
                <div className="bg-thermal-gold/10 p-3 rounded-lg">
                  <MapPin className="w-6 h-6 text-thermal-gold" />
                </div>
                <div className="space-y-1">
                  <h3 className="font-semibold text-thermal-dark">Adres</h3>
                  <div className="text-thermal-dark font-medium">
                    Gazlıgöl Mevkii, İhsaniye, Afyonkarahisar
                  </div>
                  <p className="text-muted-foreground text-sm">Gazlıgöl kaplıcaları üzerinde</p>
                </div>
              </div>
            </Card>

            <Card className="p-6 shadow-card-thermal">
              <div className="flex items-start space-x-4">
                <div className="bg-thermal-blue/10 p-3 rounded-lg">
                  <Clock className="w-6 h-6 text-thermal-blue" />
                </div>
                <div className="space-y-1">
                  <h3 className="font-semibold text-thermal-dark">Çalışma Saatleri</h3>
                  <div className="text-thermal-dark font-medium">7/24 Açık</div>
                  <p className="text-muted-foreground text-sm">Resepsiyon ve rezervasyon hizmeti</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Contact Form */}
          <Card className="p-8 shadow-card-thermal animate-fade-in-right">
          
            <h3 className="text-2xl font-bold text-thermal-dark mb-6">
              Sizi Arayalım
            </h3>
            
            <form className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-thermal-dark mb-2">
                    Adınız Soyadınız
                  </label>
                  <input
                    type="text"
                    className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Ad Soyad"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-thermal-dark mb-2">
                    Telefon Numaranız
                  </label>
                  <input
                    type="tel"
                    className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="+90 5XX XXX XX XX"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-thermal-dark mb-2">
                  Ne zaman arayalım?
                </label>
                <select className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                  <option value="">Seçiniz</option>
                  <option value="now">Hemen Ara</option>
                  <option value="15min">15 Dakika Sonra</option>
                  <option value="30min">30 Dakika Sonra</option>
                  <option value="1hour">1 Saat Sonra</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-thermal-dark mb-2">
                  Mesajınız (İsteğe Bağlı)
                </label>
                <textarea
                  rows={4}
                  className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                  placeholder="Rezervasyon detayları veya sorularınızı yazabilirsiniz..."
                ></textarea>
              </div>

              <Button type="submit" variant="thermal" className="w-full" size="lg">
                <Phone className="w-5 h-5 mr-2" />
                Beni Arayın
              </Button>
            </form>

            <div className="mt-6 pt-6 border-t border-border text-center">
              <p className="text-sm text-muted-foreground mb-4">
                Veya doğrudan WhatsApp üzerinden yazın
              </p>
              <Button variant="whatsapp" className="gap-2">
                <MessageCircle className="w-5 h-5" />
                WhatsApp ile Yazın
              </Button>
            </div>
          </Card>
        </div>

        {/* Quick Reservation Section */}
        <div className="mt-16 bg-thermal-gradient rounded-2xl p-8 text-center text-white animate-fade-in-up">
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            Hızlı Rezervasyon
          </h3>
          <p className="text-white/90 text-lg mb-6 max-w-2xl mx-auto">
            Termal tatil deneyiminizi bugün planlayın. Özel fırsatlardan yararlanmak için hemen arayın.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button variant="gold" size="xl" className="gap-2">
              <Phone className="w-5 h-5" />
              +90 272 412 48 66
            </Button>
            <div className="text-thermal-gold font-semibold">
              • Otelde Ödeme • 24/7 Destek
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;