import { Card } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";
import { useImageSlideEffect } from "@/hooks/useImageSlideEffect";
import { useTextSlideEffect } from "@/hooks/useTextSlideEffect";

const About = () => {
  const { visibleImages, imageRefs } = useImageSlideEffect();
  const { visibleElements, elementRefs } = useTextSlideEffect();
  const features = [
    "Gazlıgöl termal suları",
    "60.000 m² geniş alan", 
    "1576 yatak kapasitesi",
    "322 oda seçeneği",
    "28 villa ve 32 apart daire",
    "Doğal kristal berraklığında sular"
  ];

  return (
    <section id="about" className="py-20 bg-thermal-light/30">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div 
            ref={(el) => (elementRefs.current[0] = el)}
            data-slide-index={0}
            className={`space-y-8 transition-all duration-1000 ease-out ${
              visibleElements.has(0) ? 'translate-x-0 opacity-100' : '-translate-x-full opacity-0'
            }`}
          >
            <div className="space-y-4">
              <div className="inline-block px-4 py-2 bg-thermal-blue/10 text-thermal-blue rounded-full font-semibold">
                Hakkımızda
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-thermal-dark">
                Özbudak Termal
                <span className="block text-thermal-blue">Tatil Köyü</span>
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Afyonkarahisar'ın Ihsaniye ilçesinde bulunan Özbudak Termal Tatil Köyü, 
                Gazlıgöl bölgesinin kristal berraklığındaki termal suları ve eşsiz doğal 
                güzellikleriyle misafirlerine sağlık, huzur ve konforu bir arada sunuyor.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-thermal-teal flex-shrink-0" />
                  <span className="text-foreground font-medium">{feature}</span>
                </div>
              ))}
            </div>

            <div className="bg-thermal-gradient p-6 rounded-xl text-white">
              <h3 className="text-xl font-bold mb-2">Özel Fırsat</h3>
              <p className="text-white/90">
                Tesis, Gazlıgöl kaplıcalarının tam üstünde konumlanmış olup, 
                termal sulardan doğrudan faydalanma imkanı sunmaktadır.
              </p>
            </div>
          </div>

          {/* Right Content - Images */}
          <div 
            ref={(el) => (elementRefs.current[1] = el)}
            data-slide-index={1}
            className={`grid grid-cols-2 gap-4 transition-all duration-1000 ease-out delay-300 ${
              visibleElements.has(1) ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
            }`}
          >
            <div className="space-y-4">
              <Card 
                ref={(el) => (imageRefs.current[0] = el)}
                data-index={0}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Termal Otel Dış Görünüm"
                  className="w-full h-48 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-r from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(0) ? 'translate-x-full' : 'translate-x-0'}`}></div>
              </Card>
              <Card 
                ref={(el) => (imageRefs.current[1] = el)}
                data-index={1}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Termal Havuz"
                  className="w-full h-32 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-b from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(1) ? 'translate-y-full' : 'translate-y-0'}`}></div>
              </Card>
            </div>
            <div className="space-y-4 pt-8">
              <Card 
                ref={(el) => (imageRefs.current[2] = el)}
                data-index={2}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Spa & Wellness"
                  className="w-full h-32 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-l from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(2) ? '-translate-x-full' : 'translate-x-0'}`}></div>
              </Card>
              <Card 
                ref={(el) => (imageRefs.current[3] = el)}
                data-index={3}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Otel Odası"
                  className="w-full h-48 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-t from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(3) ? '-translate-y-full' : 'translate-y-0'}`}></div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;