import { Card } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";
import { useImageSlideEffect } from "@/hooks/useImageSlideEffect";

const About = () => {
  const { visibleImages, imageRefs } = useImageSlideEffect();
  const features = [
    "Gazlıgöl Termal Suları",
    "60.000 m² Geniş Alan", 
    "666 Yatak Kapasitesi",
    "Wellnes ve Spa Hizmetleri",
    "28 Billa ve 160 1+1 Suit",
    "Doğal Kaynağından Berrak Sular"
  ];

  return (
    <section id="about" className="py-20 bg-thermal-light/30">
      <div className="container mx-auto px-4">
                 <div className="grid lg:grid-cols-2 gap-12 items-center animate-fade-in-up">
          {/* Left Content */}
          <div className="space-y-8 animate-fade-in-left">
            <div className="space-y-4">
              <div className="inline-block px-4 py-2 bg-thermal-blue/10 text-thermal-blue rounded-full font-semibold">
                Hakkımızda
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-thermal-dark">
                Özbudak Termal
                <span className="block text-thermal-blue">Otel & Tatil Köyü</span>
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Afyonkarahisar'ın Ihsaniye ilçesinde bulunan Özbudak Termal Otel, 
                Gazlıgöl bölgesinin kristal berraklığındaki termal suları ve eşsiz doğal 
                güzellikleriyle misafirlerine sağlık, huzur ve konforu bir arada sunuyor. 
                Afyon'un en iyi termal oteli olarak hizmet veriyoruz.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-thermal-teal flex-shrink-0" />
                  <span className="text-foreground font-medium">{feature}</span>
                </div>
              ))}
            </div>

            <div className="bg-thermal-gradient p-6 rounded-xl text-white">
              <h3 className="text-xl font-bold mb-2">Afyon Termal Su Avantajı</h3>
              <p className="text-white/90">
                Özbudak Termal Otel, Gazlıgöl kaplıcalarının tam üstünde konumlanmış olup, 
                Afyon'un eşsiz termal sulardan doğrudan faydalanma imkanı sunmaktadır. 
                Termal otel deneyiminin en iyisi burada!
              </p>
            </div>
          </div>

          {/* Right Content - Images */}
          <div className="grid grid-cols-2 gap-4 animate-fade-in-right">
            <div className="space-y-4">
              <Card 
                ref={(el) => (imageRefs.current[0] = el)}
                data-index={0}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="/images/gallery/DJI_0014-scaled.webp"
                  alt="Termal Otel Dış Görünüm"
                  className="w-full h-48 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-r from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(0) ? 'translate-x-full' : 'translate-x-0'}`}></div>
              </Card>
              <Card 
                ref={(el) => (imageRefs.current[1] = el)}
                data-index={1}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="/images/gallery/DJI_0057-scaled.webp"
                  alt="Termal Havuz"
                  className="w-full h-32 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-b from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(1) ? 'translate-y-full' : 'translate-y-0'}`}></div>
              </Card>
            </div>
            <div className="space-y-4 pt-8">
              <Card 
                ref={(el) => (imageRefs.current[2] = el)}
                data-index={2}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="/images/gallery/DSCF9774-scaled.webp"
                  alt="Spa & Wellness"
                  className="w-full h-32 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-l from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(2) ? '-translate-x-full' : 'translate-x-0'}`}></div>
              </Card>
              <Card 
                ref={(el) => (imageRefs.current[3] = el)}
                data-index={3}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="/images/gallery/DSCF9825-scaled.webp"
                  alt="Otel Odası"
                  className="w-full h-48 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-t from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(3) ? '-translate-y-full' : 'translate-y-0'}`}></div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;