import { lazy, Suspense } from "react";
import Header from "@/components/Header";
import BottomMenu from "@/components/BottomMenu";
import LazySection from "@/components/LazySection";
import BottomActionButtons from "@/components/BottomActionButtons";
import BackToTop from "@/components/BackToTop";

// Lazy load components
const Hero = lazy(() => import("@/components/Hero"));
const Reservation = lazy(() => import("@/components/Reservation"));
const About = lazy(() => import("@/components/About"));
const Rooms = lazy(() => import("@/components/Rooms"));
const Facilities = lazy(() => import("@/components/Facilities"));
const Gallery = lazy(() => import("@/components/Gallery"));
const Contact = lazy(() => import("@/components/Contact"));


const Index = () => {
  return (
    <div className="min-h-screen pb-16 md:pb-0">
      <Header />
      <main>
        <Suspense fallback={
          <div className="h-[530px] lg:h-[850px] flex items-center justify-center bg-thermal-light/30">
            <div className="text-center space-y-4">
              <div className="w-12 h-12 border-4 border-thermal-blue border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="text-thermal-blue font-medium">Hero yükleniyor...</p>
            </div>
          </div>
        }>
          <Hero />
        </Suspense>

        <Suspense fallback={
          <div className="py-8 flex items-center justify-center bg-thermal-light/30">
            <div className="text-center space-y-4">
              <div className="w-10 h-10 border-4 border-thermal-blue border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="text-thermal-blue font-medium">Rezervasyon yükleniyor...</p>
            </div>
          </div>
        }>
          <Reservation />
        </Suspense>

        <Suspense fallback={
          <div className="py-20 flex items-center justify-center bg-thermal-light/30">
            <div className="text-center space-y-4">
              <div className="w-10 h-10 border-4 border-thermal-blue border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="text-thermal-blue font-medium">Hakkımızda yükleniyor...</p>
            </div>
          </div>
        }>
          <About />
        </Suspense>

        <Suspense fallback={
          <div className="py-20 flex items-center justify-center bg-thermal-light/30">
            <div className="text-center space-y-4">
              <div className="w-10 h-10 border-4 border-thermal-blue border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="text-thermal-blue font-medium">Odalar yükleniyor...</p>
            </div>
          </div>
        }>
          <Rooms />
        </Suspense>

        <Suspense fallback={
          <div className="py-20 flex items-center justify-center bg-thermal-light/30">
            <div className="text-center space-y-4">
              <div className="w-10 h-10 border-4 border-thermal-blue border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="text-thermal-blue font-medium">Tesisler yükleniyor...</p>
            </div>
          </div>
        }>
          <Facilities />
        </Suspense>

        <Suspense fallback={
          <div className="py-20 flex items-center justify-center bg-thermal-light/30">
            <div className="text-center space-y-4">
              <div className="w-10 h-10 border-4 border-thermal-blue border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="text-thermal-blue font-medium">Galeri yükleniyor...</p>
            </div>
          </div>
        }>
          <Gallery />
        </Suspense>

        <Suspense fallback={
          <div className="py-20 flex items-center justify-center bg-thermal-light/30">
            <div className="text-center space-y-4">
              <div className="w-10 h-10 border-4 border-thermal-blue border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="text-thermal-blue font-medium">İletişim yükleniyor...</p>
            </div>
          </div>
        }>
          <Contact />
        </Suspense>
      </main>
      <Suspense fallback={null}>
        <BottomActionButtons />
      </Suspense>

      <BackToTop />
      <BottomMenu />

    </div>
  );
};

export default Index;
