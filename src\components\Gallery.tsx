import { useState, useEffect, useRef } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import { useTextSlideEffect } from "@/hooks/useTextSlideEffect";

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState<number | null>(null);
  const [visibleImages, setVisibleImages] = useState<Set<number>>(new Set());
  const imageRefs = useRef<(HTMLDivElement | null)[]>([]);
  const { visibleElements, elementRefs } = useTextSlideEffect();

  const images = [
    {
      src: "/images/gallery/DJI_0002-scaled.webp",
      alt: "Otel Dış Görünüm",
      category: "Otel"
    },
    {
      src: "/images/gallery/DJI_0008-scaled.webp",
      alt: "Termal Havuz",
      category: "Havuz"
    },
    {
      src: "/images/gallery/11.webp",
      alt: "Otel Odası",
      category: "Odalar"
    },
    {
      src: "/images/gallery/DSCF9751-scaled.webp",
      alt: "Spa & Wellness",
      category: "Spa"
    },
    {
      src: "/images/gallery/OZBUDAK-RESTURANT1.webp",
      alt: "Restoran",
      category: "Restoran"
    },
    {
      src: "/images/gallery/DJI_0047-scaled.webp",
      alt: "Doğa Manzarası",
      category: "Doğa"
    },
    {
      src: "/images/gallery/DSCF9794-scaled.webp",
      alt: "Fitness Center",
      category: "Fitness"
    },
    {
      src: "/images/gallery/DJI_0053-scaled.webp",
      alt: "Aktivite Alanları",
      category: "Aktivite"
    }
  ];

  const nextImage = () => {
    if (selectedImage !== null) {
      setSelectedImage((selectedImage + 1) % images.length);
    }
  };

  const prevImage = () => {
    if (selectedImage !== null) {
      setSelectedImage(selectedImage === 0 ? images.length - 1 : selectedImage - 1);
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = parseInt(entry.target.getAttribute('data-index') || '0');
          setVisibleImages(prev => {
            const newSet = new Set(prev);
            if (entry.isIntersecting) {
              newSet.add(index);
            } else {
              newSet.delete(index);
            }
            return newSet;
          });
        });
      },
      { threshold: 0.3 }
    );

    imageRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => observer.disconnect();
  }, []);

  return (
    <section id="gallery" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div 
          ref={(el) => (elementRefs.current[0] = el)}
          data-slide-index={0}
          className={`text-center space-y-4 mb-16 transition-all duration-1000 ease-out ${
            visibleElements.has(0) ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
          }`}
        >
          <div className="inline-block px-4 py-2 bg-thermal-gold/10 text-thermal-gold rounded-full font-semibold">
            Galeri
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-thermal-dark">
            Foto
            <span className="block text-thermal-gold">Galerisi</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Tesisimizden ve sunduğumuz hizmetlerden görüntüler ile tanışın.
          </p>
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image, index) => (
            <Card 
              key={index} 
              ref={(el) => (imageRefs.current[index] = el)}
              data-index={index}
              className="relative overflow-hidden cursor-pointer group shadow-card-thermal hover:shadow-thermal transition-all duration-300"
              onClick={() => setSelectedImage(index)}
            >
              <img
                src={image.src}
                alt={image.alt}
                className="w-full h-48 md:h-56 object-cover group-hover:scale-105 transition-transform duration-500"
              />
              {/* Mavi gradient overlay - scroll'da sağa kayar */}
              <div className={`absolute inset-0 bg-gradient-to-r from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(index) ? 'translate-x-full' : 'translate-x-0'}`}></div>
              <div className="absolute bottom-4 left-4 right-4">
                <div className={`bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full transition-opacity duration-500 ${visibleImages.has(index) ? 'opacity-100' : 'opacity-0'}`}>
                  <span className="text-sm font-medium text-thermal-dark">{image.category}</span>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Lightbox Modal */}
        {selectedImage !== null && (
          <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
            <div className="relative max-w-4xl w-full">
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-4 right-4 z-10 bg-white/10 hover:bg-white/20 text-white"
                onClick={() => setSelectedImage(null)}
              >
                <X className="w-6 h-6" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 text-white"
                onClick={prevImage}
              >
                <ChevronLeft className="w-6 h-6" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 text-white"
                onClick={nextImage}
              >
                <ChevronRight className="w-6 h-6" />
              </Button>

              <img
                src={images[selectedImage].src}
                alt={images[selectedImage].alt}
                className="w-full h-auto max-h-[80vh] object-contain rounded-lg"
              />

              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full">
                <span className="font-medium text-thermal-dark">
                  {images[selectedImage].alt}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Call to Action */}
        <div 
          ref={(el) => (elementRefs.current[1] = el)}
          data-slide-index={1}
          className={`text-center mt-16 transition-all duration-1000 ease-out delay-500 ${
            visibleElements.has(1) ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'
          }`}
        >
          <div className="bg-thermal-light/50 backdrop-blur-sm rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-thermal-dark mb-4">
              Daha Fazla Görmek İster Misiniz?
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Tesisimizi yakından tanımak ve rezervasyon yapmak için bizimle iletişime geçin.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button variant="thermal" size="lg">
                Sanal Tur İçin Ara
              </Button>
              <span className="text-thermal-blue font-semibold">+90 272 444 0 444</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Gallery;