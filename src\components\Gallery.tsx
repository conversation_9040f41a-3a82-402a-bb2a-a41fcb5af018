import { useState, useEffect, useRef } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import { useTextSlideEffect } from "@/hooks/useTextSlideEffect";

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState<number | null>(null);
  const [visibleImages, setVisibleImages] = useState<Set<number>>(new Set());
  const imageRefs = useRef<(HTMLDivElement | null)[]>([]);
  const { visibleElements, elementRefs } = useTextSlideEffect();

  const images = [
    { src: "/images/gallery/DJI_0002-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DJI_0008-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DJI_0010-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DJI_0014-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DJI_0047-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DJI_0048-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DJI_0053-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DJI_0057-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9751-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9756-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9767-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9774-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9794-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9818-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9825-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9837-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9839-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9867-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9884-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9896-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9908-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9916-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9935-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/DSCF9942-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/OZBUDAK-RESTURANT1.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5649-Kopya-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5665-Kopya-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5705-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5717-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5722-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5741-Kopya-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5752-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5759-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5779-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5783-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5811-Kopya-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5827-Kopya-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/YA_5829-scaled.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/10.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/11.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/13.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/14.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/16.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/18.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/19.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/2.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/20.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/21.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/22.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/23.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/24.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/4.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/5.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/6.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/7.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/8.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/9.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/full01.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/full03.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/ozbudak-buhar-odasi.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/ozbudak-sauna.webp", alt: "Otel Görünümü" },
    { src: "/images/gallery/ozbudak-tuz-odasi.webp", alt: "Otel Görünümü" }
  ];

  const nextImage = () => {
    if (selectedImage !== null) {
      setSelectedImage((selectedImage + 1) % images.length);
    }
  };

  const prevImage = () => {
    if (selectedImage !== null) {
      setSelectedImage(selectedImage === 0 ? images.length - 1 : selectedImage - 1);
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = parseInt(entry.target.getAttribute('data-index') || '0');
          setVisibleImages(prev => {
            const newSet = new Set(prev);
            if (entry.isIntersecting) {
              newSet.add(index);
            } else {
              newSet.delete(index);
            }
            return newSet;
          });
        });
      },
      { threshold: 0.3 }
    );

    imageRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => observer.disconnect();
  }, []);

  return (
    <section id="gallery" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div 
          ref={(el) => (elementRefs.current[0] = el)}
          data-slide-index={0}
          className={`text-center space-y-4 mb-16 transition-all duration-1000 ease-out ${
            visibleElements.has(0) ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
          }`}
        >
          <div className="inline-block px-4 py-2 bg-thermal-gold/10 text-thermal-gold rounded-full font-semibold">
            Galeri
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-thermal-dark">
            Foto
            <span className="block text-thermal-gold">Galerisi</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Tesisimizden ve sunduğumuz hizmetlerden görüntüler ile tanışın.
          </p>
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image, index) => (
            <Card 
              key={index} 
              ref={(el) => (imageRefs.current[index] = el)}
              data-index={index}
              className="relative overflow-hidden cursor-pointer group shadow-card-thermal hover:shadow-thermal transition-all duration-300"
              onClick={() => setSelectedImage(index)}
            >
              <img
                src={image.src}
                alt={image.alt}
                className="w-full h-48 md:h-56 object-cover group-hover:scale-105 transition-transform duration-500"
              />
              {/* Mavi gradient overlay - scroll'da sağa kayar */}
              <div className={`absolute inset-0 bg-gradient-to-r from-thermal-blue/70 via-thermal-blue/50 to-thermal-blue/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(index) ? 'translate-x-full' : 'translate-x-0'}`}></div>
            </Card>
          ))}
        </div>

        {/* Lightbox Modal */}
        {selectedImage !== null && (
          <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
            <div className="relative max-w-4xl w-full">
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-4 right-4 z-10 bg-white/10 hover:bg-white/20 text-white"
                onClick={() => setSelectedImage(null)}
              >
                <X className="w-6 h-6" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 text-white"
                onClick={prevImage}
              >
                <ChevronLeft className="w-6 h-6" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 text-white"
                onClick={nextImage}
              >
                <ChevronRight className="w-6 h-6" />
              </Button>

              <img
                src={images[selectedImage].src}
                alt={images[selectedImage].alt}
                className="w-full h-auto max-h-[80vh] object-contain rounded-lg"
              />

              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full">
                <span className="font-medium text-thermal-dark">
                  {images[selectedImage].alt}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Call to Action */}
        <div 
          ref={(el) => (elementRefs.current[1] = el)}
          data-slide-index={1}
          className={`text-center mt-16 transition-all duration-1000 ease-out delay-500 ${
            visibleElements.has(1) ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'
          }`}
        >
          <div className="bg-thermal-light/50 backdrop-blur-sm rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-thermal-dark mb-4">
              Daha Fazla Görmek İster Misiniz?
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Tesisimizi yakından tanımak ve rezervasyon yapmak için bizimle iletişime geçin.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button variant="thermal" size="lg">
                Sanal Tur İçin Ara
              </Button>
              <span className="text-thermal-blue font-semibold">+90 272 444 0 444</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Gallery;